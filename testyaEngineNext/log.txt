  [PER got  1<=1016 bits => span 1 +10[1..1016]:26 (1015) => 0x0] (./asn_bit_data.c:132)
  [PER got  5<=1015 bits => span 6 +10[6..1016]:26 (1010) => 0x9] (./asn_bit_data.c:132)
CHOICE RasMessage got index 9 in range 5 (./constr_CHOICE_aper.c:50)
Discovered CHOICE RasMessage encodes admissionRequest (./constr_CHOICE_aper.c:85)
Decoding AdmissionRequest as SEQUENCE (APER) (./constr_SEQUENCE_aper.c:40)
  [PER got  1<=1010 bits => span 7 +10[7..1016]:26 (1009) => 0x1] (./asn_bit_data.c:132)
  [PER got  7<=1009 bits => span 14 +10[14..1016]:26 (1002) => 0x24] (./asn_bit_data.c:132)
Read in presence bitmap for AdmissionRequest of 7 bits (48..) (./constr_SEQUENCE_aper.c:62)
Decoding member "requestSeqNum" in AdmissionRequest (./constr_SEQUENCE_aper.c:130)
Decoding NativeInteger RequestSeqNum (APER) (./NativeInteger_aper.c:21)
Integer with range 16 bits (./INTEGER_aper.c:54)
Aligning 2 bits (./aper_support.c:13)
  [PER got  2<=1002 bits => span 16 +11[8..1008]:90 (1000) => 0x0] (./asn_bit_data.c:132)
  [PER got 16<=1000 bits => span 32 +12[16..1000]:5f (984) => 0x5f59] (./asn_bit_data.c:132)
Got value 24410 + low 1 (./INTEGER_aper.c:114)
NativeInteger RequestSeqNum got value 24410 (./NativeInteger_aper.c:37)
Freeing INTEGER as a primitive type (./asn_codecs_prim.c:16)
Decoding member "callType" in AdmissionRequest (./constr_SEQUENCE_aper.c:130)
  [PER got  1<=984 bits => span 33 +14[1..984]:03 (983) => 0x0] (./asn_bit_data.c:132)
  [PER got  2<=983 bits => span 35 +14[3..984]:03 (981) => 0x0] (./asn_bit_data.c:132)
CHOICE CallType got index 0 in range 2 (./constr_CHOICE_aper.c:50)
Discovered CHOICE CallType encodes pointToPoint (./constr_CHOICE_aper.c:85)
Aligning 5 bits (./aper_support.c:13)
  [PER got  5<=981 bits => span 40 +14[8..984]:03 (976) => 0x3] (./asn_bit_data.c:132)
  [PER got  1<= 7 bits => span 1 +0[1..7]:48 (6) => 0x0] (./asn_bit_data.c:132)
Member AdmissionRequest->callModel is optional, p=0 (1->7) (./constr_SEQUENCE_aper.c:110)
Decoding member "endpointIdentifier" in AdmissionRequest (./constr_SEQUENCE_aper.c:130)
APER decoding ASN_OSUBV_U16 range_bits = 16
 (./OCTET_STRING_aper.c:85)
PER Decoding non-extensible size 1 .. 128 bits 7 (./OCTET_STRING_aper.c:117)
aper get constrained_whole_number with lb 1 and ub 128 (./aper_support.c:123)
  [PER got  7<=976 bits => span 47 +15[7..976]:c0 (969) => 0x60] (./asn_bit_data.c:132)
Got PER length eb 7, len 97, once (EndpointIdentifier) (./OCTET_STRING_aper.c:197)
Aligning 1 bits (./aper_support.c:13)
  [PER got  1<=969 bits => span 48 +15[8..976]:c0 (968) => 0x0] (./asn_bit_data.c:132)
Expanding 97 characters into (0..65533):16 (./OCTET_STRING.c:300)
  [PER got 24<=968 bits => span 72 +0[24..968]:00 (944) => 0x3500] (./asn_bit_data.c:132)
  [PER got 24<=944 bits => span 96 +3[24..944]:32 (920) => 0x320042] (./asn_bit_data.c:132)
  [PER got 24<=920 bits => span 120 +6[24..920]:00 (896) => 0x3800] (./asn_bit_data.c:132)
  [PER got 24<=896 bits => span 144 +9[24..896]:38 (872) => 0x380030] (./asn_bit_data.c:132)
  [PER got 24<=872 bits => span 168 +12[24..872]:00 (848) => 0x4600] (./asn_bit_data.c:132)
  [PER got 24<=848 bits => span 192 +15[24..848]:43 (824) => 0x430030] (./asn_bit_data.c:132)
  [PER got 24<=824 bits => span 216 +2[24..824]:00 (800) => 0x3000] (./asn_bit_data.c:132)
  [PER got 24<=800 bits => span 240 +5[24..800]:30 (776) => 0x300030] (./asn_bit_data.c:132)
  [PER got 24<=776 bits => span 264 +8[24..776]:00 (752) => 0x3000] (./asn_bit_data.c:132)
  [PER got 24<=752 bits => span 288 +11[24..752]:30 (728) => 0x300030] (./asn_bit_data.c:132)
  [PER got 24<=728 bits => span 312 +14[24..728]:00 (704) => 0x3301] (./asn_bit_data.c:132)
  [PER got 24<=704 bits => span 336 +1[24..704]:04 (680) => 0x4807c] (./asn_bit_data.c:132)
  [PER got 24<=680 bits => span 360 +4[24..680]:ca (656) => 0xca7839] (./asn_bit_data.c:132)
  [PER got 24<=656 bits => span 384 +7[24..656]:5c (632) => 0x5c0140] (./asn_bit_data.c:132)
  [PER got 24<=632 bits => span 408 +10[24..632]:04 (608) => 0x40048] (./asn_bit_data.c:132)
  [PER got 24<=608 bits => span 432 +13[24..608]:00 (584) => 0x2d00] (./asn_bit_data.c:132)
  [PER got 24<=584 bits => span 456 +0[24..584]:53 (560) => 0x530052] (./asn_bit_data.c:132)
  [PER got 24<=560 bits => span 480 +3[24..560]:00 (536) => 0x5600] (./asn_bit_data.c:132)
  [PER got 24<=536 bits => span 504 +6[24..536]:55 (512) => 0x555a61] (./asn_bit_data.c:132)
  [PER got 24<=512 bits => span 528 +9[24..512]:41 (488) => 0x41e4a7] (./asn_bit_data.c:132)
  [PER got 24<=488 bits => span 552 +12[24..488]:40 (464) => 0x400a3d] (./asn_bit_data.c:132)
  [PER got 24<=464 bits => span 576 +15[24..464]:0d (440) => 0xd6c8a] (./asn_bit_data.c:132)
  [PER got 24<=440 bits => span 600 +2[24..440]:30 (416) => 0x3016ff] (./asn_bit_data.c:132)
  [PER got 24<=416 bits => span 624 +5[24..416]:29 (392) => 0x290100] (./asn_bit_data.c:132)
  [PER got 24<=392 bits => span 648 +8[24..392]:10 (368) => 0x10091c] (./asn_bit_data.c:132)
  [PER got 24<=368 bits => span 672 +11[24..368]:5c (344) => 0x5c987a] (./asn_bit_data.c:132)
  [PER got 24<=344 bits => span 696 +14[24..344]:a9 (320) => 0xa98517] (./asn_bit_data.c:132)
  [PER got 24<=320 bits => span 720 +1[24..320]:08 (296) => 0x8e420] (./asn_bit_data.c:132)
  [PER got 24<=296 bits => span 744 +4[24..296]:00 (272) => 0x180] (./asn_bit_data.c:132)
  [PER got 24<=272 bits => span 768 +7[24..272]:11 (248) => 0x11008a] (./asn_bit_data.c:132)
  [PER got 24<=248 bits => span 792 +10[24..248]:30 (224) => 0x3016ff] (./asn_bit_data.c:132)
  [PER got 24<=224 bits => span 816 +13[24..224]:29 (200) => 0x290100] (./asn_bit_data.c:132)
  [PER got 24<=200 bits => span 840 +0[24..200]:10 (176) => 0x10091b] (./asn_bit_data.c:132)
  [PER got 24<=176 bits => span 864 +3[24..176]:5c (152) => 0x5c987a] (./asn_bit_data.c:132)
  [PER got 24<=152 bits => span 888 +6[24..152]:a9 (128) => 0xa98517] (./asn_bit_data.c:132)
  [PER got 24<=128 bits => span 912 +9[24..128]:0d (104) => 0xd0a00] (./asn_bit_data.c:132)
  [PER got 24<=104 bits => span 936 +12[24..104]:47 (80) => 0x47006b] (./asn_bit_data.c:132)
  [PER got 24<=80 bits => span 960 +15[24..80]:00 (56) => 0x3700] (./asn_bit_data.c:132)
  [PER got 24<=56 bits => span 984 +2[24..56]:32 (32) => 0x320030] (./asn_bit_data.c:132)
  [PER got 24<=32 bits => span 1008 +5[24..32]:00 (8) => 0x3601] (./asn_bit_data.c:132)
Failed decode endpointIdentifier in AdmissionRequest (./constr_SEQUENCE_aper.c:145)
Failed to decode admissionRequest in RasMessage (CHOICE) 1 (./constr_CHOICE_aper.c:96)
Freeing RasMessage as CHOICE (./constr_CHOICE.c:168)
Freeing AdmissionRequest as SEQUENCE (./constr_SEQUENCE.c:80)
Freeing RequestSeqNum as INTEGER (1, 0x3dde8af8, Native) (./NativeInteger.c:110)
Freeing CallType as CHOICE (./constr_CHOICE.c:168)
Freeing EndpointIdentifier as OCTET STRING (./OCTET_STRING.c:113)
Freeing BandWidth as INTEGER (1, 0x3dde8ba0, Native) (./NativeInteger.c:110)
Freeing CallReferenceValue as INTEGER (1, 0x3dde8ba8, Native) (./NativeInteger.c:110)
Freeing ConferenceIdentifier as OCTET STRING (./OCTET_STRING.c:113)
Decoding H323-UserInformation as SEQUENCE (APER) (./constr_SEQUENCE_aper.c:40)
  [PER got  1<=1536 bits => span 1 +2[1..1536]:20 (1535) => 0x0] (./asn_bit_data.c:132)
  [PER got  1<=1535 bits => span 2 +2[2..1536]:20 (1534) => 0x0] (./asn_bit_data.c:132)
Read in presence bitmap for H323-UserInformation of 1 bits (0..) (./constr_SEQUENCE_aper.c:62)
Decoding member "h323-uu-pdu" in H323-UserInformation (./constr_SEQUENCE_aper.c:130)
Decoding H323-UU-PDU as SEQUENCE (APER) (./constr_SEQUENCE_aper.c:40)
  [PER got  1<=1534 bits => span 3 +2[3..1536]:20 (1533) => 0x1] (./asn_bit_data.c:132)
  [PER got  1<=1533 bits => span 4 +2[4..1536]:20 (1532) => 0x0] (./asn_bit_data.c:132)
Read in presence bitmap for H323-UU-PDU of 1 bits (0..) (./constr_SEQUENCE_aper.c:62)
Decoding member "h323-message-body" in H323-UU-PDU (./constr_SEQUENCE_aper.c:130)
  [PER got  1<=1532 bits => span 5 +2[5..1536]:20 (1531) => 0x0] (./asn_bit_data.c:132)
  [PER got  3<=1531 bits => span 8 +2[8..1536]:20 (1528) => 0x0] (./asn_bit_data.c:132)
CHOICE h323-message-body got index 0 in range 3 (./constr_CHOICE_aper.c:50)
Discovered CHOICE h323-message-body encodes setup (./constr_CHOICE_aper.c:85)
Decoding Setup-UUIE as SEQUENCE (APER) (./constr_SEQUENCE_aper.c:40)
  [PER got  1<=1528 bits => span 9 +3[1..1528]:a8 (1527) => 0x1] (./asn_bit_data.c:132)
  [PER got  7<=1527 bits => span 16 +3[8..1528]:a8 (1520) => 0x28] (./asn_bit_data.c:132)
Read in presence bitmap for Setup-UUIE of 7 bits (50..) (./constr_SEQUENCE_aper.c:62)
Decoding member "protocolIdentifier" in Setup-UUIE (./constr_SEQUENCE_aper.c:130)
APER decoding ASN_OSUBV_STR range_bits = 8 unit_bits = 8
 (./OCTET_STRING_aper.c:76)
PER Decoding non-extensible size 0 .. 0 bits -1 (./OCTET_STRING_aper.c:117)
  [PER got  8<=1520 bits => span 24 +4[8..1520]:06 (1512) => 0x6] (./asn_bit_data.c:132)
Got PER length eb -1, len 6, once (ProtocolIdentifier) (./OCTET_STRING_aper.c:197)
Expanding 6 characters into (0..255):8 (./OCTET_STRING.c:300)
  [PER got 24<=1512 bits => span 48 +5[24..1512]:00 (1488) => 0x891] (./asn_bit_data.c:132)
  [PER got 24<=1488 bits => span 72 +8[24..1488]:4a (1464) => 0x4a0004] (./asn_bit_data.c:132)
  [PER got  1<= 7 bits => span 1 +0[1..7]:50 (6) => 0x0] (./asn_bit_data.c:132)
Member Setup-UUIE->h245Address is optional, p=0 (1->7) (./constr_SEQUENCE_aper.c:110)
  [PER got  1<= 6 bits => span 2 +0[2..7]:50 (5) => 0x1] (./asn_bit_data.c:132)
Member Setup-UUIE->sourceAddress is optional, p=1 (2->7) (./constr_SEQUENCE_aper.c:110)
Decoding member "sourceAddress" in Setup-UUIE (./constr_SEQUENCE_aper.c:130)
  [PER got  8<=1464 bits => span 80 +11[8..1464]:01 (1456) => 0x1] (./asn_bit_data.c:132)
Got to decode 1 elements (eff -1) (./constr_SET_OF_aper.c:156)
SET OF AliasAddress decoding (./constr_SET_OF_aper.c:163)
  [PER got  1<=1456 bits => span 81 +12[1..1456]:40 (1455) => 0x0] (./asn_bit_data.c:132)
  [PER got  1<=1455 bits => span 82 +12[2..1456]:40 (1454) => 0x1] (./asn_bit_data.c:132)
CHOICE AliasAddress got index 1 in range 1 (./constr_CHOICE_aper.c:50)
Discovered CHOICE AliasAddress encodes h323-ID (./constr_CHOICE_aper.c:85)
APER decoding ASN_OSUBV_U16 range_bits = 16
 (./OCTET_STRING_aper.c:85)
PER Decoding non-extensible size 1 .. 256 bits 8 (./OCTET_STRING_aper.c:117)
aper get constrained_whole_number with lb 1 and ub 256 (./aper_support.c:123)
Aligning 6 bits (./aper_support.c:13)
  [PER got  6<=1454 bits => span 88 +12[8..1456]:40 (1448) => 0x0] (./asn_bit_data.c:132)
  [PER got  8<=1448 bits => span 96 +13[8..1448]:04 (1440) => 0x4] (./asn_bit_data.c:132)
Got PER length eb 8, len 5, once (BMPString) (./OCTET_STRING_aper.c:197)
Expanding 5 characters into (0..65533):16 (./OCTET_STRING.c:300)
  [PER got 24<=1440 bits => span 120 +14[24..1440]:00 (1416) => 0x4800] (./asn_bit_data.c:132)
  [PER got 24<=1416 bits => span 144 +1[24..1416]:2d (1392) => 0x2d0053] (./asn_bit_data.c:132)
  [PER got 24<=1392 bits => span 168 +4[24..1392]:00 (1368) => 0x5200] (./asn_bit_data.c:132)
  [PER got  8<=1368 bits => span 176 +7[8..1368]:56 (1360) => 0x56] (./asn_bit_data.c:132)
sourceAddress SET OF AliasAddress decoded 0, 0x3ddeaa00 (./constr_SET_OF_aper.c:166)
Decoded sourceAddress as SET OF (./constr_SET_OF_aper.c:186)
Decoding member "sourceInfo" in Setup-UUIE (./constr_SEQUENCE_aper.c:130)
Decoding EndpointType as SEQUENCE (APER) (./constr_SEQUENCE_aper.c:40)
  [PER got  1<=1360 bits => span 177 +8[1..1360]:00 (1359) => 0x0] (./asn_bit_data.c:132)
  [PER got  6<=1359 bits => span 183 +8[7..1360]:00 (1353) => 0x0] (./asn_bit_data.c:132)
Read in presence bitmap for EndpointType of 6 bits (0..) (./constr_SEQUENCE_aper.c:62)
  [PER got  1<= 6 bits => span 1 +0[1..6]:00 (5) => 0x0] (./asn_bit_data.c:132)
Member EndpointType->nonStandardData is optional, p=0 (1->6) (./constr_SEQUENCE_aper.c:110)
  [PER got  1<= 5 bits => span 2 +0[2..6]:00 (4) => 0x0] (./asn_bit_data.c:132)
Member EndpointType->vendor is optional, p=0 (2->6) (./constr_SEQUENCE_aper.c:110)
  [PER got  1<= 4 bits => span 3 +0[3..6]:00 (3) => 0x0] (./asn_bit_data.c:132)
Member EndpointType->gatekeeper is optional, p=0 (3->6) (./constr_SEQUENCE_aper.c:110)
  [PER got  1<= 3 bits => span 4 +0[4..6]:00 (2) => 0x0] (./asn_bit_data.c:132)
Member EndpointType->gateway is optional, p=0 (4->6) (./constr_SEQUENCE_aper.c:110)
  [PER got  1<= 2 bits => span 5 +0[5..6]:00 (1) => 0x0] (./asn_bit_data.c:132)
Member EndpointType->mcu is optional, p=0 (5->6) (./constr_SEQUENCE_aper.c:110)
  [PER got  1<= 1 bits => span 6 +0[6..6]:00 (0) => 0x0] (./asn_bit_data.c:132)
Member EndpointType->terminal is optional, p=0 (6->6) (./constr_SEQUENCE_aper.c:110)
Decoding member "mc" in EndpointType (./constr_SEQUENCE_aper.c:130)
  [PER got  1<=1353 bits => span 184 +8[8..1360]:00 (1352) => 0x0] (./asn_bit_data.c:132)
BOOLEAN decoded as FALSE (./BOOLEAN_aper.c:39)
Decoding member "undefinedNode" in EndpointType (./constr_SEQUENCE_aper.c:130)
  [PER got  1<=1352 bits => span 185 +9[1..1352]:00 (1351) => 0x0] (./asn_bit_data.c:132)
BOOLEAN decoded as FALSE (./BOOLEAN_aper.c:39)
  [PER got  1<= 5 bits => span 3 +0[3..7]:50 (4) => 0x0] (./asn_bit_data.c:132)
Member Setup-UUIE->destinationAddress is optional, p=0 (3->7) (./constr_SEQUENCE_aper.c:110)
  [PER got  1<= 4 bits => span 4 +0[4..7]:50 (3) => 0x1] (./asn_bit_data.c:132)
Member Setup-UUIE->destCallSignalAddress is optional, p=1 (4->7) (./constr_SEQUENCE_aper.c:110)
Decoding member "destCallSignalAddress" in Setup-UUIE (./constr_SEQUENCE_aper.c:130)
  [PER got  1<=1351 bits => span 186 +9[2..1352]:00 (1350) => 0x0] (./asn_bit_data.c:132)
  [PER got  3<=1350 bits => span 189 +9[5..1352]:00 (1347) => 0x0] (./asn_bit_data.c:132)
CHOICE TransportAddress got index 0 in range 3 (./constr_CHOICE_aper.c:50)
Discovered CHOICE TransportAddress encodes ipAddress (./constr_CHOICE_aper.c:85)
Decoding ipAddress as SEQUENCE (APER) (./constr_SEQUENCE_aper.c:40)
Decoding member "ip" in ipAddress (./constr_SEQUENCE_aper.c:130)
PER Decoding non-extensible size 4 .. 4 bits 0 (./OCTET_STRING_aper.c:117)
Aligning 3 bits (./aper_support.c:13)
  [PER got  3<=1347 bits => span 192 +9[8..1352]:00 (1344) => 0x0] (./asn_bit_data.c:132)
Decoding OCTET STRING size 4 (./OCTET_STRING_aper.c:154)
Expanding 4 characters into (0..0):8 (./OCTET_STRING.c:300)
  [PER got 24<=1344 bits => span 216 +10[24..1344]:0a (1320) => 0xa9b00] (./asn_bit_data.c:132)
  [PER got  8<=1320 bits => span 224 +13[8..1320]:cc (1312) => 0xcc] (./asn_bit_data.c:132)
Decoding member "port" in ipAddress (./constr_SEQUENCE_aper.c:130)
Decoding NativeInteger INTEGER (APER) (./NativeInteger_aper.c:21)
Integer with range 16 bits (./INTEGER_aper.c:54)
  [PER got 16<=1312 bits => span 240 +14[16..1312]:06 (1296) => 0x6b8] (./asn_bit_data.c:132)
Got value 1720 + low 0 (./INTEGER_aper.c:114)
NativeInteger INTEGER got value 1720 (./NativeInteger_aper.c:37)
Freeing INTEGER as a primitive type (./asn_codecs_prim.c:16)
  [PER got  1<= 3 bits => span 5 +0[5..7]:50 (2) => 0x0] (./asn_bit_data.c:132)
Member Setup-UUIE->destExtraCallInfo is optional, p=0 (5->7) (./constr_SEQUENCE_aper.c:110)
  [PER got  1<= 2 bits => span 6 +0[6..7]:50 (1) => 0x0] (./asn_bit_data.c:132)
Member Setup-UUIE->destExtraCRV is optional, p=0 (6->7) (./constr_SEQUENCE_aper.c:110)
Decoding member "activeMC" in Setup-UUIE (./constr_SEQUENCE_aper.c:130)
  [PER got  1<=1296 bits => span 241 +0[1..1296]:00 (1295) => 0x0] (./asn_bit_data.c:132)
BOOLEAN decoded as FALSE (./BOOLEAN_aper.c:39)
Decoding member "conferenceID" in Setup-UUIE (./constr_SEQUENCE_aper.c:130)
PER Decoding non-extensible size 16 .. 16 bits 0 (./OCTET_STRING_aper.c:117)
Aligning 7 bits (./aper_support.c:13)
  [PER got  7<=1295 bits => span 248 +0[8..1296]:00 (1288) => 0x0] (./asn_bit_data.c:132)
Decoding OCTET STRING size 16 (./OCTET_STRING_aper.c:154)
Expanding 16 characters into (0..0):8 (./OCTET_STRING.c:300)
  [PER got 24<=1288 bits => span 272 +1[24..1288]:8a (1264) => 0x8a3016] (./asn_bit_data.c:132)
  [PER got 24<=1264 bits => span 296 +4[24..1264]:ff (1240) => 0xff2901] (./asn_bit_data.c:132)
  [PER got 24<=1240 bits => span 320 +7[24..1240]:00 (1216) => 0x1009] (./asn_bit_data.c:132)
  [PER got 24<=1216 bits => span 344 +10[24..1216]:1c (1192) => 0x1c5c98] (./asn_bit_data.c:132)
  [PER got 24<=1192 bits => span 368 +13[24..1192]:7a (1168) => 0x7aa985] (./asn_bit_data.c:132)
  [PER got  8<=1168 bits => span 376 +0[8..1168]:17 (1160) => 0x17] (./asn_bit_data.c:132)
Decoding member "conferenceGoal" in Setup-UUIE (./constr_SEQUENCE_aper.c:130)
  [PER got  1<=1160 bits => span 377 +1[1..1160]:00 (1159) => 0x0] (./asn_bit_data.c:132)
  [PER got  2<=1159 bits => span 379 +1[3..1160]:00 (1157) => 0x0] (./asn_bit_data.c:132)
CHOICE conferenceGoal got index 0 in range 2 (./constr_CHOICE_aper.c:50)
Discovered CHOICE conferenceGoal encodes create (./constr_CHOICE_aper.c:85)
Aligning 5 bits (./aper_support.c:13)
  [PER got  5<=1157 bits => span 384 +1[8..1160]:00 (1152) => 0x0] (./asn_bit_data.c:132)
  [PER got  1<= 1 bits => span 7 +0[7..7]:50 (0) => 0x0] (./asn_bit_data.c:132)
Member Setup-UUIE->callServices is optional, p=0 (7->7) (./constr_SEQUENCE_aper.c:110)
Decoding member "callType" in Setup-UUIE (./constr_SEQUENCE_aper.c:130)
  [PER got  1<=1152 bits => span 385 +2[1..1152]:cd (1151) => 0x1] (./asn_bit_data.c:132)
Discovered CHOICE CallType encodes oneToN (./constr_CHOICE_aper.c:85)
Getting open type NULL... (./aper_opentype.c:25)
Aligning 7 bits (./aper_support.c:13)
  [PER got  7<=1151 bits => span 392 +2[8..1152]:cd (1144) => 0x4d] (./asn_bit_data.c:132)
  [PER got  8<=1144 bits => span 400 +3[8..1144]:1d (1136) => 0x1d] (./asn_bit_data.c:132)
  [PER got 24<=1136 bits => span 424 +4[24..1136]:82 (1112) => 0x820007] (./asn_bit_data.c:132)
  [PER got 24<=1112 bits => span 448 +7[24..1112]:00 (1088) => 0x555a] (./asn_bit_data.c:132)
  [PER got 24<=1088 bits => span 472 +10[24..1088]:61 (1064) => 0x614106] (./asn_bit_data.c:132)
  [PER got 24<=1064 bits => span 496 +13[24..1064]:b8 (1040) => 0xb81100] (./asn_bit_data.c:132)
  [PER got 24<=1040 bits => span 520 +0[24..1040]:8a (1016) => 0x8a3016] (./asn_bit_data.c:132)
  [PER got 24<=1016 bits => span 544 +3[24..1016]:ff (992) => 0xff2901] (./asn_bit_data.c:132)
  [PER got 24<=992 bits => span 568 +6[24..992]:00 (968) => 0x1009] (./asn_bit_data.c:132)
  [PER got 24<=968 bits => span 592 +9[24..968]:1b (944) => 0x1b5c98] (./asn_bit_data.c:132)
  [PER got 24<=944 bits => span 616 +12[24..944]:7a (920) => 0x7aa985] (./asn_bit_data.c:132)
  [PER got 16<=920 bits => span 632 +15[16..920]:17 (904) => 0x1763] (./asn_bit_data.c:132)
Getting open type NULL encoded in 29 bytes (./aper_opentype.c:50)
Too large padding 232 in open type (./aper_opentype.c:74)
Failed to decode element NULL (./aper_opentype.c:75)
Failed to decode oneToN in CallType (CHOICE) 2 (./constr_CHOICE_aper.c:96)
Failed decode callType in Setup-UUIE (./constr_SEQUENCE_aper.c:145)
Failed to decode setup in h323-message-body (CHOICE) 2 (./constr_CHOICE_aper.c:96)
Failed decode h323-message-body in H323-UU-PDU (./constr_SEQUENCE_aper.c:145)
Failed decode h323-uu-pdu in H323-UserInformation (./constr_SEQUENCE_aper.c:145)
Freeing H323-UserInformation as SEQUENCE (./constr_SEQUENCE.c:80)
Freeing H323-UU-PDU as SEQUENCE (./constr_SEQUENCE.c:80)
Freeing h323-message-body as CHOICE (./constr_CHOICE.c:168)
Freeing Setup-UUIE as SEQUENCE (./constr_SEQUENCE.c:80)
Freeing ProtocolIdentifier as a primitive type (./asn_codecs_prim.c:16)
Freeing AliasAddress as CHOICE (./constr_CHOICE.c:168)
Freeing BMPString as OCTET STRING (./OCTET_STRING.c:113)
Freeing EndpointType as SEQUENCE (./constr_SEQUENCE.c:80)
Freeing TransportAddress as CHOICE (./constr_CHOICE.c:168)
Freeing ipAddress as SEQUENCE (./constr_SEQUENCE.c:80)
Freeing OCTET STRING as OCTET STRING (./OCTET_STRING.c:113)
Freeing INTEGER as INTEGER (1, 0x3ddeab80, Native) (./NativeInteger.c:110)
Freeing ConferenceIdentifier as OCTET STRING (./OCTET_STRING.c:113)
Freeing conferenceGoal as CHOICE (./constr_CHOICE.c:168)
Freeing CallType as CHOICE (./constr_CHOICE.c:168)
  [PER got  1<=728 bits => span 1 +10[1..728]:3e (727) => 0x0] (./asn_bit_data.c:132)
  [PER got  5<=727 bits => span 6 +10[6..728]:3e (722) => 0xf] (./asn_bit_data.c:132)
CHOICE RasMessage got index 15 in range 5 (./constr_CHOICE_aper.c:50)
Discovered CHOICE RasMessage encodes disengageRequest (./constr_CHOICE_aper.c:85)
Decoding DisengageRequest as SEQUENCE (APER) (./constr_SEQUENCE_aper.c:40)
  [PER got  1<=722 bits => span 7 +10[7..728]:3e (721) => 0x1] (./asn_bit_data.c:132)
  [PER got  1<=721 bits => span 8 +10[8..728]:3e (720) => 0x0] (./asn_bit_data.c:132)
Read in presence bitmap for DisengageRequest of 1 bits (0..) (./constr_SEQUENCE_aper.c:62)
Decoding member "requestSeqNum" in DisengageRequest (./constr_SEQUENCE_aper.c:130)
Decoding NativeInteger RequestSeqNum (APER) (./NativeInteger_aper.c:21)
Integer with range 16 bits (./INTEGER_aper.c:54)
  [PER got 16<=720 bits => span 24 +11[16..720]:5f (704) => 0x5f5a] (./asn_bit_data.c:132)
Got value 24411 + low 1 (./INTEGER_aper.c:114)
NativeInteger RequestSeqNum got value 24411 (./NativeInteger_aper.c:37)
Freeing INTEGER as a primitive type (./asn_codecs_prim.c:16)
Decoding member "endpointIdentifier" in DisengageRequest (./constr_SEQUENCE_aper.c:130)
APER decoding ASN_OSUBV_U16 range_bits = 16
 (./OCTET_STRING_aper.c:85)
PER Decoding non-extensible size 1 .. 128 bits 7 (./OCTET_STRING_aper.c:117)
aper get constrained_whole_number with lb 1 and ub 128 (./aper_support.c:123)
  [PER got  7<=704 bits => span 31 +13[7..704]:1e (697) => 0xf] (./asn_bit_data.c:132)
Got PER length eb 7, len 16, once (EndpointIdentifier) (./OCTET_STRING_aper.c:197)
Aligning 1 bits (./aper_support.c:13)
  [PER got  1<=697 bits => span 32 +13[8..704]:1e (696) => 0x0] (./asn_bit_data.c:132)
Expanding 16 characters into (0..65533):16 (./OCTET_STRING.c:300)
  [PER got 24<=696 bits => span 56 +14[24..696]:00 (672) => 0x3500] (./asn_bit_data.c:132)
  [PER got 24<=672 bits => span 80 +1[24..672]:32 (648) => 0x320042] (./asn_bit_data.c:132)
  [PER got 24<=648 bits => span 104 +4[24..648]:00 (624) => 0x3800] (./asn_bit_data.c:132)
  [PER got 24<=624 bits => span 128 +7[24..624]:38 (600) => 0x380030] (./asn_bit_data.c:132)
  [PER got 24<=600 bits => span 152 +10[24..600]:00 (576) => 0x4600] (./asn_bit_data.c:132)
  [PER got 24<=576 bits => span 176 +13[24..576]:43 (552) => 0x430030] (./asn_bit_data.c:132)
  [PER got 24<=552 bits => span 200 +0[24..552]:00 (528) => 0x3000] (./asn_bit_data.c:132)
  [PER got 24<=528 bits => span 224 +3[24..528]:30 (504) => 0x300030] (./asn_bit_data.c:132)
  [PER got 24<=504 bits => span 248 +6[24..504]:00 (480) => 0x3000] (./asn_bit_data.c:132)
  [PER got 24<=480 bits => span 272 +9[24..480]:30 (456) => 0x300030] (./asn_bit_data.c:132)
  [PER got 16<=456 bits => span 288 +12[16..456]:00 (440) => 0x33] (./asn_bit_data.c:132)
Decoding member "conferenceID" in DisengageRequest (./constr_SEQUENCE_aper.c:130)
PER Decoding non-extensible size 16 .. 16 bits 0 (./OCTET_STRING_aper.c:117)
Decoding OCTET STRING size 16 (./OCTET_STRING_aper.c:154)
Expanding 16 characters into (0..0):8 (./OCTET_STRING.c:300)
  [PER got 24<=440 bits => span 312 +14[24..440]:8a (416) => 0x8a3016] (./asn_bit_data.c:132)
  [PER got 24<=416 bits => span 336 +1[24..416]:ff (392) => 0xff2901] (./asn_bit_data.c:132)
  [PER got 24<=392 bits => span 360 +4[24..392]:00 (368) => 0x1009] (./asn_bit_data.c:132)
  [PER got 24<=368 bits => span 384 +7[24..368]:1c (344) => 0x1c5c98] (./asn_bit_data.c:132)
  [PER got 24<=344 bits => span 408 +10[24..344]:7a (320) => 0x7aa985] (./asn_bit_data.c:132)
  [PER got  8<=320 bits => span 416 +13[8..320]:17 (312) => 0x17] (./asn_bit_data.c:132)
Decoding member "callReferenceValue" in DisengageRequest (./constr_SEQUENCE_aper.c:130)
Decoding NativeInteger CallReferenceValue (APER) (./NativeInteger_aper.c:21)
Integer with range 16 bits (./INTEGER_aper.c:54)
  [PER got 16<=312 bits => span 432 +14[16..312]:0d (296) => 0xd6c] (./asn_bit_data.c:132)
Got value 3436 + low 0 (./INTEGER_aper.c:114)
NativeInteger CallReferenceValue got value 3436 (./NativeInteger_aper.c:37)
Freeing INTEGER as a primitive type (./asn_codecs_prim.c:16)
Decoding member "disengageReason" in DisengageRequest (./constr_SEQUENCE_aper.c:130)
  [PER got  1<=296 bits => span 433 +0[1..296]:23 (295) => 0x0] (./asn_bit_data.c:132)
  [PER got  2<=295 bits => span 435 +0[3..296]:23 (293) => 0x1] (./asn_bit_data.c:132)
CHOICE DisengageReason got index 1 in range 2 (./constr_CHOICE_aper.c:50)
Discovered CHOICE DisengageReason encodes normalDrop (./constr_CHOICE_aper.c:85)
Aligning 5 bits (./aper_support.c:13)
  [PER got  5<=293 bits => span 440 +0[8..296]:23 (288) => 0x3] (./asn_bit_data.c:132)
  [PER got  1<= 1 bits => span 1 +0[1..1]:00 (0) => 0x0] (./asn_bit_data.c:132)
Member DisengageRequest->nonStandardData is optional, p=0 (1->1) (./constr_SEQUENCE_aper.c:110)
Getting normally small length (./aper_support.c:56)
  [PER got  1<=288 bits => span 441 +1[1..288]:31 (287) => 0x0] (./asn_bit_data.c:132)
  [PER got  6<=287 bits => span 447 +1[7..288]:31 (281) => 0x18] (./asn_bit_data.c:132)
l=25 (./aper_support.c:61)
Extensions 25 present in DisengageRequest (./constr_SEQUENCE_aper.c:166)
  [PER got 24<=281 bits => span 471 +1[31..288]:31 (257) => 0x800880] (./asn_bit_data.c:132)
  [PER got  1<=257 bits => span 472 +4[8..264]:00 (256) => 0x0] (./asn_bit_data.c:132)
Read in extensions bitmap for DisengageRequest of 25 bits (80..) (./constr_SEQUENCE_aper.c:178)
  [PER got  1<=25 bits => span 1 +0[1..25]:80 (24) => 0x1] (./asn_bit_data.c:132)
Decoding member callIdentifier in DisengageRequest (nil) (./constr_SEQUENCE_aper.c:211)
Getting open type CallIdentifier... (./aper_opentype.c:25)
  [PER got  8<=256 bits => span 480 +5[8..256]:8a (248) => 0x8a] (./asn_bit_data.c:132)
  [PER got  8<=248 bits => span 488 +6[8..248]:30 (240) => 0x30] (./asn_bit_data.c:132)
  [PER got 24<=240 bits => span 512 +7[24..240]:16 (216) => 0x16ff29] (./asn_bit_data.c:132)
  [PER got 24<=216 bits => span 536 +10[24..216]:01 (192) => 0x10010] (./asn_bit_data.c:132)
  [PER got 24<=192 bits => span 560 +13[24..192]:09 (168) => 0x91b5c] (./asn_bit_data.c:132)
  [PER got 24<=168 bits => span 584 +0[24..168]:98 (144) => 0x987aa9] (./asn_bit_data.c:132)
  [PER got 24<=144 bits => span 608 +3[24..144]:85 (120) => 0x85170d] (./asn_bit_data.c:132)
  [PER got 24<=120 bits => span 632 +6[24..120]:0a (96) => 0xa0047] (./asn_bit_data.c:132)
  [PER got 24<=96 bits => span 656 +9[24..96]:00 (72) => 0x6b00] (./asn_bit_data.c:132)
  [PER got 24<=72 bits => span 680 +12[24..72]:37 (48) => 0x370032] (./asn_bit_data.c:132)
  [PER got 24<=48 bits => span 704 +15[24..48]:00 (24) => 0x3000] (./asn_bit_data.c:132)
  [PER got 24<=24 bits => span 728 +2[24..24]:36 (0) => 0x360100] (./asn_bit_data.c:132)
Failed to decode disengageRequest in RasMessage (CHOICE) 1 (./constr_CHOICE_aper.c:96)
Freeing RasMessage as CHOICE (./constr_CHOICE.c:168)
Freeing DisengageRequest as SEQUENCE (./constr_SEQUENCE.c:80)
Freeing RequestSeqNum as INTEGER (1, 0x3ddec5e8, Native) (./NativeInteger.c:110)
Freeing EndpointIdentifier as OCTET STRING (./OCTET_STRING.c:113)
Freeing ConferenceIdentifier as OCTET STRING (./OCTET_STRING.c:113)
Freeing CallReferenceValue as INTEGER (1, 0x3ddec640, Native) (./NativeInteger.c:110)
Freeing DisengageReason as CHOICE (./constr_CHOICE.c:168)
