

#include <yaBasicUtils/allocator.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_mbuf.h>
#include <yaProtoRecord/precord.h>

#include <cstdint>
#include <gmock/gmock.h>
#include <gtest/gtest.h>
#include <vector>

extern "C" {
#include "AdmissionRequest.h"
#include "AliasAddress.h"
#include "CallType.h"
#include "RasMessage.h"
#include "TransportAddress.h"
#include "asn1_discovery.h"
#include "per_decoder.h"
}
/*
H.225.0 CS
    H323-UserInformation
        h323-uu-pdu
            h323-message-body: setup (0)
                setup
                    protocolIdentifier: 0.0.8.2250.0.4 (Version 4)
                    sourceAddress: 1 item
                        Item 0
                            AliasAddress: h323-ID (1)
                                h323-ID: H-SRV
                    sourceInfo
                        .... ...0 mc: False
                        0... .... undefinedNode: False
                    destCallSignalAddress: ipAddress (0)
                        ipAddress
                            ip: ************
                            port: 1720
                    0... .... activeMC: False
                    conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
                    conferenceGoal: create (0)
                        create: NULL
                    callType: pointToPoint (0)
                        pointToPoint: NULL
                    sourceCallSignalAddress: ipAddress (0)
                        ipAddress
                            ip: ***********
                            port: 1720
                    callIdentifier
                        guid: 8a3016ff-2901-0010-091b-5c987aa98517
                    fastStart: 4 items
                        Item 0
                            FastStart item: 18 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 1
                                forwardLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (3)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40199
                        Item 1
                            FastStart item: 29 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 1
                                forwardLogicalChannelParameters
                                    dataType: nullData (1)
                                        nullData: NULL
                                    multiplexParameters: none (4)
                                        none: NULL
                                reverseLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Ulaw64k (3)
                                            g711Ulaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (2)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40198
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40199
                        Item 2
                            FastStart item: 18 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 2
                                forwardLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Alaw64k (1)
                                            g711Alaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (3)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40199
                        Item 3
                            FastStart item: 29 octets
                            OpenLogicalChannel
                                forwardLogicalChannelNumber: 2
                                forwardLogicalChannelParameters
                                    dataType: nullData (1)
                                        nullData: NULL
                                    multiplexParameters: none (4)
                                        none: NULL
                                reverseLogicalChannelParameters
                                    dataType: audioData (3)
                                        audioData: g711Alaw64k (1)
                                            g711Alaw64k: 20
                                    multiplexParameters: h2250LogicalChannelParameters (2)
                                        h2250LogicalChannelParameters
                                            sessionID: 1
                                            mediaChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40198
                                            mediaControlChannel: unicastAddress (0)
                                                unicastAddress: iPAddress (0)
                                                    iPAddress
                                                        network: ************
                                                        tsapIdentifier: 40199
                    0... .... mediaWaitForConnect: False
                    0... .... canOverlapSend: False
                    1... .... multipleCalls: True
                    0... .... maintainConnection: False
                    symmetricOperationRequired: NULL
            1... .... h245Tunnelling: True
*/
std::vector<uint8_t> H323_UserInformation_SetUP[] = {
  0x20, 0xa8, 0x06, 0x00, 0x08, 0x91, 0x4a, 0x00,
  0x04, 0x01, 0x40, 0x04, 0x00, 0x48, 0x00, 0x2d,
  0x00, 0x53, 0x00, 0x52, 0x00, 0x56, 0x00, 0x00,
  0x0a, 0x9b, 0x00, 0xcc, 0x06, 0xb8, 0x00, 0x8a,
  0x30, 0x16, 0xff, 0x29, 0x01, 0x00, 0x10, 0x09,
  0x1c, 0x5c, 0x98, 0x7a, 0xa9, 0x85, 0x17, 0x00,
  0xcd, 0x1d, 0x82, 0x00, 0x07, 0x00, 0x55, 0x5a,
  0x61, 0x41, 0x06, 0xb8, 0x11, 0x00, 0x8a, 0x30,
  0x16, 0xff, 0x29, 0x01, 0x00, 0x10, 0x09, 0x1b,
  0x5c, 0x98, 0x7a, 0xa9, 0x85, 0x17, 0x63, 0x04,
  0x12, 0x00, 0x00, 0x00, 0x0c, 0x60, 0x13, 0x80,
  0x0a, 0x04, 0x00, 0x01, 0x00, 0x55, 0x5a, 0x63,
  0xa1, 0x9d, 0x07, 0x1d, 0x40, 0x00, 0x00, 0x06,
  0x04, 0x01, 0x00, 0x4c, 0x60, 0x13, 0x80, 0x11,
  0x14, 0x00, 0x01, 0x00, 0x55, 0x5a, 0x63, 0xa1,
  0x9d, 0x06, 0x00, 0x55, 0x5a, 0x63, 0xa1, 0x9d,
  0x07, 0x12, 0x00, 0x00, 0x01, 0x0c, 0x20, 0x13,
  0x80, 0x0a, 0x04, 0x00, 0x01, 0x00, 0x55, 0x5a,
  0x63, 0xa1, 0x9d, 0x07, 0x1d, 0x40, 0x00, 0x01,
  0x06, 0x04, 0x01, 0x00, 0x4c, 0x20, 0x13, 0x80,
  0x11, 0x14, 0x00, 0x01, 0x00, 0x55, 0x5a, 0x63,
  0xa1, 0x9d, 0x06, 0x00, 0x55, 0x5a, 0x63, 0xa1,
  0x9d, 0x07, 0x01, 0x00, 0x01, 0x00, 0x01, 0x80,
  0x01, 0x00, 0x01, 0x00, 0x10, 0x80, 0x01, 0x80
};


/*
H.225.0 RAS
    RasMessage: disengageRequest (15)
        disengageRequest
            requestSeqNum: 24411
            endpointIdentifier: 52B880FC00000003
            conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
            callReferenceValue: 3436
            disengageReason: normalDrop (1)
                normalDrop: NULL
            callIdentifier
                guid: 8a3016ff-2901-0010-091b-5c987aa98517
            gatekeeperIdentifier: Gk7206
            0... .... answeredCall: False
*/
std::vector<uint8_t> RasMessage_disengageRequest = {
  0x3e, 0x5f, 0x5a, 0x1e, 0x00, 0x35, 0x00, 0x32,
  0x00, 0x42, 0x00, 0x38, 0x00, 0x38, 0x00, 0x30,
  0x00, 0x46, 0x00, 0x43, 0x00, 0x30, 0x00, 0x30,
  0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30,
  0x00, 0x30, 0x00, 0x33, 0x8a, 0x30, 0x16, 0xff,
  0x29, 0x01, 0x00, 0x10, 0x09, 0x1c, 0x5c, 0x98,
  0x7a, 0xa9, 0x85, 0x17, 0x0d, 0x6c, 0x23, 0x31,
  0x00, 0x11, 0x00, 0x8a, 0x30, 0x16, 0xff, 0x29,
  0x01, 0x00, 0x10, 0x09, 0x1b, 0x5c, 0x98, 0x7a,
  0xa9, 0x85, 0x17, 0x0d, 0x0a, 0x00, 0x47, 0x00,
  0x6b, 0x00, 0x37, 0x00, 0x32, 0x00, 0x30, 0x00,
  0x36, 0x01, 0x00
};


// H.225.0 RAS
//     RasMessage: disengageConfirm (16)
//         disengageConfirm
//             requestSeqNum: 24411
//     [This is a response to a request in frame 3]
//     [RAS Service Response Time: 0.000814000 seconds]
std::vector<uint8_t> RasMessage_disengageConfirm = {
  0x40, 0x5f, 0x5a
};

class H225RasAsn1Test : public ::testing::Test {
protected:
  void SetUp() override {
    alloc = ya_allocator_get_default();
    nxt_engine_config_t config = {.linkName = "eth", .trailerName = nullptr};
    engine = nxt_engine_create(&config);
    ASSERT_NE(engine, nullptr);
  }

  void TearDown() override {
    if (engine) {
      nxt_engine_destroy(engine);
    }
  }

  ya_allocator_t *alloc;
  nxt_engine_t *engine;
};


/*
H.225.0 RAS
    RasMessage: admissionRequest (9)
        admissionRequest
            requestSeqNum: 24410
            callType: pointToPoint (0)
                pointToPoint: NULL
            endpointIdentifier: 52B880FC00000003
            destinationInfo: 1 item
                Item 0
                    DestinationInfo item: dialledDigits (0)
                        dialledDigits: 4997450629
            srcInfo: 1 item
                Item 0
                    AliasAddress: h323-ID (1)
                        h323-ID: H-SRV
            srcCallSignalAddress: ipAddress (0)
                ipAddress
                    ip: ***********
                    port: 58535
            bandWidth: 2621
            callReferenceValue: 3436
            conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
            0... .... activeMC: False
            .0.. .... answerCall: False
            1... .... canMapAlias: True
            callIdentifier
                guid: 8a3016ff-2901-0010-091b-5c987aa98517
            gatekeeperIdentifier: Gk7206
            0... .... willSupplyUUIEs: False
*/
std::vector<uint8_t> RasMessage_admissionRequest_ = {
    0x26, 0x90, 0x5f, 0x59, 0x03, 0xc0, 0x00, 0x35, 0x00, 0x32, 0x00, 0x42,
    0x00, 0x38, 0x00, 0x38, 0x00, 0x30, 0x00, 0x46, 0x00, 0x43, 0x00, 0x30,
    0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30,
    0x00, 0x33, 0x01, 0x04, 0x80, 0x7c, 0xca, 0x78, 0x39, 0x5c, 0x01, 0x40,
    0x04, 0x00, 0x48, 0x00, 0x2d, 0x00, 0x53, 0x00, 0x52, 0x00, 0x56, 0x00,
    0x55, 0x5a, 0x61, 0x41, 0xe4, 0xa7, 0x40, 0x0a, 0x3d, 0x0d, 0x6c, 0x8a,
    0x30, 0x16, 0xff, 0x29, 0x01, 0x00, 0x10, 0x09, 0x1c, 0x5c, 0x98, 0x7a,
    0xa9, 0x85, 0x17, 0x08, 0xe4, 0x20, 0x00, 0x01, 0x80, 0x11, 0x00, 0x8a,
    0x30, 0x16, 0xff, 0x29, 0x01, 0x00, 0x10, 0x09, 0x1b, 0x5c, 0x98, 0x7a,
    0xa9, 0x85, 0x17, 0x0d, 0x0a, 0x00, 0x47, 0x00, 0x6b, 0x00, 0x37, 0x00,
    0x32, 0x00, 0x30, 0x00, 0x36, 0x01, 0x00};

TEST_F(H225RasAsn1Test, ASN1_ParseAdmissionRequest) {
  asn_dec_rval_t decode_result;
  RasMessage_t *RasInfo = NULL;

  // 测试UPER解码完整功能
  decode_result = aper_decode_complete(
      NULL, &asn_DEF_RasMessage, (void **)&RasInfo,
      RasMessage_admissionRequest_.data(), RasMessage_admissionRequest_.size());

  if (decode_result.code == RC_OK) {
    printf("H225RAS: UPER decode complete successful\n");

    // 验证解析结果
    ASSERT_NE(RasInfo, nullptr);
    ASSERT_EQ(RasInfo->present, RasMessage_PR_admissionRequest);

    // 验证AdmissionRequest的关键字段
    AdmissionRequest_t *admReq = &RasInfo->choice.admissionRequest;
    printf("Request Seq Num: %ld (expected: 24410)\n", admReq->requestSeqNum);
    printf("Bandwidth: %ld (expected: 2621)\n", admReq->bandWidth);
    printf("Call Reference Value: %ld (expected: 3436)\n",
           admReq->callReferenceValue);

    // 验证callType
    printf("Call Type present: %d\n", admReq->callType.present);

    // 验证endpointIdentifier
    printf("Endpoint Identifier: %.*s\n", (int)admReq->endpointIdentifier.size,
           admReq->endpointIdentifier.buf);

    // 验证destinationInfo
    if (admReq->destinationInfo) {
      printf("Destination Info count: %d\n",
             admReq->destinationInfo->list.count);
      if (admReq->destinationInfo->list.count > 0) {
        struct AliasAddress *destAlias = admReq->destinationInfo->list.array[0];
        printf("Destination present: %d\n", destAlias->present);
        if (destAlias->present == AliasAddress_PR_dialledDigits) {
          printf("Destination: %.*s\n",
                 (int)destAlias->choice.dialledDigits.size,
                 destAlias->choice.dialledDigits.buf);
        }
      }
    }

    // 验证srcInfo
    printf("Source Info count: %d\n", admReq->srcInfo.list.count);
    if (admReq->srcInfo.list.count > 0) {
      struct AliasAddress *srcAlias = admReq->srcInfo.list.array[0];
      printf("Source present: %d\n", srcAlias->present);
      if (srcAlias->present == AliasAddress_PR_h323_ID) {
        printf("Source: %.*s\n", (int)srcAlias->choice.h323_ID.size,
               srcAlias->choice.h323_ID.buf);
      }
    }

    // 验证srcCallSignalAddress
    if (admReq->srcCallSignalAddress) {
      printf("Source call signal address present: %d\n",
             admReq->srcCallSignalAddress->present);
    }

    // 验证conferenceID
    printf("Conference ID size: %zu\n", admReq->conferenceID.size);

    // 验证callIdentifier
    if (admReq->callIdentifier) {
      printf("Call Identifier GUID size: %zu\n",
             admReq->callIdentifier->guid.size);
    }

    // 验证gatekeeperIdentifier
    if (admReq->gatekeeperIdentifier) {
      printf("Gatekeeper ID: %.*s\n", (int)admReq->gatekeeperIdentifier->size,
             admReq->gatekeeperIdentifier->buf);
    }

    printf("=== H.225 RAS AdmissionRequest 解析成功 ===\n");
    printf("Request Seq Num: %ld\n", admReq->requestSeqNum);
    printf("Bandwidth: %ld\n", admReq->bandWidth);
    printf("Call Reference Value: %ld\n", admReq->callReferenceValue);

    // 验证关键字段值是否符合预期
    EXPECT_EQ(admReq->requestSeqNum, 24410);
    EXPECT_EQ(admReq->bandWidth, 2621);
    EXPECT_EQ(admReq->callReferenceValue, 3436);

  } else {
    printf("H225RAS: UPER decode complete failed (consumed: %zu bytes)\n",
           decode_result.consumed);
  }
  // 尝试其他解码策略
  if (RasInfo) {
    ASN_STRUCT_FREE(asn_DEF_RasMessage, RasInfo);
    RasInfo = NULL;
  }
  // 清理资源
  if (RasInfo) {
    ASN_STRUCT_FREE(asn_DEF_RasMessage, RasInfo);
  }
}
