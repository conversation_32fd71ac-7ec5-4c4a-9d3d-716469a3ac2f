#include <stdbool.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <yaBasicUtils/macro.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaFtypes/fvalue.h>
#include <yaProtoRecord/precord.h>
#define PROTO_NAME "h225"

#include "asn1_discovery.h"

static int dissect_h225_H323UserInformation(nxt_engine_t *engine,
                                            nxt_session_t *session,
                                            nxt_mbuf_t *mbuf) {
  // Check minimum length
  if (nxt_mbuf_get_length(mbuf) < 4) {
    return NXT_DISSECT_ST_VERIFY_FAILED;
  }

  const uint8_t *data;
  size_t data_len;
  // Decode H.323 User Information using ASN.1
  // H.225 uses PER (Packed Encoding Rules) encoding as per ITU-T standard
  H323_UserInformation_t *h323_ui = NULL;
  // Get data from mbuf
  data = nxt_mbuf_get_raw(mbuf, 0);
  data_len = nxt_mbuf_get_length(mbuf);

  // Additional validation
  if (!data || data_len < 4) {
    printf("H225: Invalid data or insufficient length\n");
    return 0;
  }
  // Try multiple decoding strategies for H.225 CS
  asn_dec_rval_t decode_result;

  // Strategy 1: Try uper_decode_complete (unaligned PER complete)
  decode_result = aper_decode_complete(NULL, &asn_DEF_H323_UserInformation,
                                       (void **)&h323_ui, data, data_len);
  if (decode_result.code == RC_OK && h323_ui) {
    printf("H225: UPER decode complete successful\n");
  } else {
    printf("H225: UPER decode complete failed (consumed: %zu bytes)\n",
           decode_result.consumed);

    // Clean up failed attempt
    if (h323_ui) {
      ASN_STRUCT_FREE(asn_DEF_H323_UserInformation, h323_ui);
      h323_ui = NULL;
    }

  }

  // Extract and print important fields if decode was successful
  if (h323_ui) {
    printf("=== H.225 CS Message Details ===\n");

    // Get message type
    H323_UU_PDU_t *uu_pdu = &h323_ui->h323_uu_pdu;
    const char *msg_type = "Unknown";

    switch (uu_pdu->h323_message_body.present) {
    case H323_UU_PDU__h323_message_body_PR_setup:
      msg_type = "Setup";
      {
        Setup_UUIE_t *setup = &uu_pdu->h323_message_body.choice.setup;

        // Call type
        const char *call_type_str = "Unknown";
        switch (setup->callType.present) {
        case CallType_PR_pointToPoint:
          call_type_str = "Point-to-Point";
          break;
        case CallType_PR_oneToN:
          call_type_str = "One-to-N";
          break;
        case CallType_PR_nToOne:
          call_type_str = "N-to-One";
          break;
        case CallType_PR_nToN:
          call_type_str = "N-to-N";
          break;
        default:
          call_type_str = "Other";
          break;
        }
        printf("Call Type: %s\n", call_type_str);
        printf("Active MC: %s\n", setup->activeMC ? "Yes" : "No");

        // Conference goal
        const char *conf_goal = "Unknown";
        switch (setup->conferenceGoal.present) {
        case Setup_UUIE__conferenceGoal_PR_create:
          conf_goal = "Create";
          break;
        case Setup_UUIE__conferenceGoal_PR_join:
          conf_goal = "Join";
          break;
        case Setup_UUIE__conferenceGoal_PR_invite:
          conf_goal = "Invite";
          break;
        default:
          conf_goal = "Other";
          break;
        }
        printf("Conference Goal: %s\n", conf_goal);

        // Optional fields
        if (setup->mediaWaitForConnect) {
          printf("Media Wait For Connect: %s\n",
                 *setup->mediaWaitForConnect ? "Yes" : "No");
        }
        if (setup->canOverlapSend) {
          printf("Can Overlap Send: %s\n",
                 *setup->canOverlapSend ? "Yes" : "No");
        }
        if (setup->fastStart && setup->fastStart->list.count > 0) {
          printf("Fast Start Elements: %d\n", setup->fastStart->list.count);
        }
      }
      break;
    case H323_UU_PDU__h323_message_body_PR_callProceeding:
      msg_type = "Call Proceeding";
      break;
    case H323_UU_PDU__h323_message_body_PR_connect:
      msg_type = "Connect";
      break;
    case H323_UU_PDU__h323_message_body_PR_alerting:
      msg_type = "Alerting";
      break;
    case H323_UU_PDU__h323_message_body_PR_releaseComplete:
      msg_type = "Release Complete";
      break;
    case H323_UU_PDU__h323_message_body_PR_facility:
      msg_type = "Facility";
      break;
    case H323_UU_PDU__h323_message_body_PR_progress:
      msg_type = "Progress";
      break;
    case H323_UU_PDU__h323_message_body_PR_status:
      msg_type = "Status";
      break;
    default:
      msg_type = "Other";
      break;
    }

    printf("Message Type: %s\n", msg_type);

    // Check H.245 tunnelling
    if (uu_pdu->h245Tunnelling) {
      printf("H.245 Tunnelling: %s\n", *uu_pdu->h245Tunnelling ? "Yes" : "No");

      // Check H.245 control messages
      if (uu_pdu->h245Control && uu_pdu->h245Control->list.count > 0) {
        printf("H.245 Control Messages: %d\n", uu_pdu->h245Control->list.count);
        for (int i = 0; i < uu_pdu->h245Control->list.count && i < 3; i++) {
          OCTET_STRING_t *ctrl_msg = uu_pdu->h245Control->list.array[i];
          if (ctrl_msg) {
            printf("  H.245 Control[%d]: %zu bytes\n", i, ctrl_msg->size);
          }
        }
      }
    }

    printf("================================\n");
  }

  // Clean up the decoded structure

  precord_t *precord = nxt_session_create_record(engine, session);
  if (!precord) {
    return NXT_DISSECT_ST_VERIFY_FAILED;
  }
  precord_layer_put_new_layer_cache(precord, PROTO_NAME);

  // nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf,
  // precord);
  if (h323_ui) {
    ASN_STRUCT_FREE(asn_DEF_H323_UserInformation, h323_ui);
    h323_ui = NULL;
  }

  return decode_result.consumed > 0 ? decode_result.consumed : data_len;
}

static inline int h225_dissect(nxt_engine_t *engine, nxt_session_t *session,
                               nxt_mbuf_t *mbuf) {
  return dissect_h225_H323UserInformation(engine, session, mbuf);
}
// Schema registration function
static int h225_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
  /* 注册 schema */
  pschema_t *pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE,
                                              PROTO_NAME, "H.225 Protocol");

  // Basic H.225 message fields
  pschema_register_field(pschema, "protocol", YA_FT_STRING, "Protocol");
  pschema_register_field(pschema, "version", YA_FT_STRING, "Version");
  pschema_register_field(pschema, "message_type", YA_FT_STRING, "Message Type");
  pschema_register_field(pschema, "protocol_type", YA_FT_STRING,
                         "Protocol Type"); // RAS or CS
  pschema_register_field(pschema, "data_length", YA_FT_UINT32, "Data Length");
  pschema_register_field(pschema, "decode_status", YA_FT_STRING,
                         "Decode Status");
  pschema_register_field(pschema, "decode_error", YA_FT_STRING, "Decode Error");

  // H.323 User Information fields
  pschema_register_field(pschema, "h323_uu_pdu_present", YA_FT_UINT32,
                         "H323-UU-PDU Present");
  pschema_register_field(pschema, "user_data_present", YA_FT_UINT32,
                         "User Data Present");

  // H.323 UU-PDU fields
  pschema_register_field(pschema, "h323_message_body_type", YA_FT_STRING,
                         "H323 Message Body Type");
  pschema_register_field(pschema, "h245_tunnelling", YA_FT_UINT32,
                         "H245 Tunnelling");
  pschema_register_field(pschema, "h245_control_present", YA_FT_UINT32,
                         "H245 Control Present");
  pschema_register_field(pschema, "h245_control_count", YA_FT_UINT32,
                         "H245 Control Count");
  pschema_register_field(pschema, "nonstandard_data_present", YA_FT_UINT32,
                         "Non-standard Data Present");

  // Protocol identifier
  pschema_register_field(pschema, "protocol_identifier", YA_FT_STRING,
                         "Protocol Identifier");

  // Call information
  pschema_register_field(pschema, "call_identifier", YA_FT_STRING,
                         "Call Identifier");
  pschema_register_field(pschema, "conference_id", YA_FT_STRING,
                         "Conference ID");

  // Endpoint information
  pschema_register_field(pschema, "source_info_present", YA_FT_UINT32,
                         "Source Info Present");
  pschema_register_field(pschema, "dest_info_present", YA_FT_UINT32,
                         "Destination Info Present");
  pschema_register_field(pschema, "dest_call_signalling_address", YA_FT_STRING,
                         "Destination Call Signalling Address");

  // Setup specific fields
  pschema_register_field(pschema, "source_address", YA_FT_STRING,
                         "Source Address");
  pschema_register_field(pschema, "dest_address", YA_FT_STRING,
                         "Destination Address");
  pschema_register_field(pschema, "dest_extra_calling", YA_FT_STRING,
                         "Destination Extra Calling");
  pschema_register_field(pschema, "dest_extra_call_info", YA_FT_STRING,
                         "Destination Extra Call Info");

  // Facility specific fields
  pschema_register_field(pschema, "facility_reason", YA_FT_STRING,
                         "Facility Reason");

  // Release complete specific fields
  pschema_register_field(pschema, "release_complete_reason", YA_FT_STRING,
                         "Release Complete Reason");

  // H.245 tunnelling fields
  pschema_register_field(pschema, "h245_control_0_size", YA_FT_UINT32,
                         "H245 Control 0 Size");
  pschema_register_field(pschema, "h245_control_0_sample", YA_FT_BYTES,
                         "H245 Control 0 Sample");
  pschema_register_field(pschema, "h245_control_1_size", YA_FT_UINT32,
                         "H245 Control 1 Size");
  pschema_register_field(pschema, "h245_control_1_sample", YA_FT_BYTES,
                         "H245 Control 1 Sample");
  pschema_register_field(pschema, "h245_control_2_size", YA_FT_UINT32,
                         "H245 Control 2 Size");
  pschema_register_field(pschema, "h245_control_2_sample", YA_FT_BYTES,
                         "H245 Control 2 Sample");

  // Setup specific boolean fields
  pschema_register_field(pschema, "active_mc", YA_FT_UINT32, "Active MC");
  pschema_register_field(pschema, "media_wait_for_connect", YA_FT_UINT32,
                         "Media Wait For Connect");
  pschema_register_field(pschema, "can_overlap_send", YA_FT_UINT32,
                         "Can Overlap Send");

  // RAS message specific fields
  pschema_register_field(pschema, "request_seq_num", YA_FT_UINT32,
                         "Request Sequence Number");
  pschema_register_field(pschema, "endpoint_identifier", YA_FT_STRING,
                         "Endpoint Identifier");
  pschema_register_field(pschema, "gatekeeper_identifier", YA_FT_STRING,
                         "Gatekeeper Identifier");
  pschema_register_field(pschema, "call_reference_value", YA_FT_UINT32,
                         "Call Reference Value");
  pschema_register_field(pschema, "bandwidth", YA_FT_UINT32, "Bandwidth");
  pschema_register_field(pschema, "reject_reason", YA_FT_STRING,
                         "Reject Reason");
  pschema_register_field(pschema, "disengage_reason", YA_FT_STRING,
                         "Disengage Reason");

  // Error handling and validation fields
  pschema_register_field(pschema, "validation_error", YA_FT_STRING,
                         "Validation Error");
  pschema_register_field(pschema, "asn1_decode_consumed", YA_FT_UINT32,
                         "ASN.1 Decode Consumed Bytes");
  pschema_register_field(pschema, "user_user_ie_offset", YA_FT_UINT32,
                         "User-User IE Offset");
  pschema_register_field(pschema, "user_user_ie_length", YA_FT_UINT32,
                         "User-User IE Length");
  pschema_register_field(pschema, "parse_warnings", YA_FT_STRING,
                         "Parse Warnings");

  return 0;
}

// H.225 dissector definition
static nxt_dissector_def_t gDissectorDef = {
    .name = PROTO_NAME,
    .type = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = h225_schema_reg,
    .dissectFun = h225_dissect,
    .handoff = NXT_HANDOFF_DEFAULT,
    .mountAt =
        {
            // H.225 CS messages are typically carried over TPKT
            // It can also be mounted directly on specific protocols
            // NXT_MNT_PORT_PAYLOAD("tcp", 1720, NULL), // H.225 call signalling
            // NXT_MNT_PORT_PAYLOAD("tcp", 1503, NULL), // T.120 data sharing
            NXT_MNT_END,
        },
};

NXT_DISSECTOR_INIT(h225) {
  nxt_dissector_register(&gDissectorDef);

  // Register handoff rule to receive calls from Q.931 (key 1) for CS messages
  nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "q931", 1, "h225");

  // Register handoff rule for H.245 processing (key 2)
  nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "h225", 2, "h245");
}
